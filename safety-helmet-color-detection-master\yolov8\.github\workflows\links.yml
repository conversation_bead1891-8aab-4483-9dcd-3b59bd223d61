# Ultralytics YOLO 🚀, AGPL-3.0 license
# Continuous Integration (CI) GitHub Actions tests broken link checker using https://github.com/lycheeverse/lychee
# Ignores the following status codes to reduce false positives:
#   - 403(OpenVINO, 'forbidden')
#   - 429(Instagram, 'too many requests')
#   - 500(<PERSON><PERSON><PERSON>, 'cached')
#   - 502(<PERSON><PERSON><PERSON>, 'bad gateway')
#   - 999(LinkedIn, 'unknown status code')

name: Check Broken links

on:
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *" # runs at 00:00 UTC every day

jobs:
  Links:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Download and install lychee
        run: |
          LYCHEE_URL=$(curl -s https://api.github.com/repos/lycheeverse/lychee/releases/latest | grep "browser_download_url" | grep "x86_64-unknown-linux-gnu.tar.gz" | cut -d '"' -f 4)
          curl -L $LYCHEE_URL -o lychee.tar.gz
          tar xzf lychee.tar.gz
          sudo mv lychee /usr/local/bin

      - name: Test Markdown and HTML links with retry
        uses: nick-invision/retry@v3
        with:
          timeout_minutes: 5
          retry_wait_seconds: 60
          max_attempts: 3
          command: |
            lychee \
            --scheme https \
            --timeout 60 \
            --insecure \
            --accept 403,429,500,502,999 \
            --exclude-all-private \
            --exclude 'https?://(www\.)?(linkedin\.com|twitter\.com|instagram\.com|kaggle\.com|fonts\.gstatic\.com|url\.com)' \
            --exclude-path docs/zh \
            --exclude-path docs/es \
            --exclude-path docs/ru \
            --exclude-path docs/pt \
            --exclude-path docs/fr \
            --exclude-path docs/de \
            --exclude-path docs/ja \
            --exclude-path docs/ko \
            --exclude-path docs/hi \
            --exclude-path docs/ar \
            --github-token ${{ secrets.GITHUB_TOKEN }} \
            './**/*.md' \
            './**/*.html'

      - name: Test Markdown, HTML, YAML, Python and Notebook links with retry
        if: github.event_name == 'workflow_dispatch'
        uses: nick-invision/retry@v3
        with:
          timeout_minutes: 5
          retry_wait_seconds: 60
          max_attempts: 3
          command: |
            lychee \
            --scheme https \
            --timeout 60 \
            --insecure \
            --accept 429,999 \
            --exclude-all-private \
            --exclude 'https?://(www\.)?(linkedin\.com|twitter\.com|instagram\.com|kaggle\.com|fonts\.gstatic\.com|url\.com)' \
            --exclude-path '**/ci.yaml' \
            --exclude-path docs/zh \
            --exclude-path docs/es \
            --exclude-path docs/ru \
            --exclude-path docs/pt \
            --exclude-path docs/fr \
            --exclude-path docs/de \
            --exclude-path docs/ja \
            --exclude-path docs/ko \
            --exclude-path docs/hi \
            --exclude-path docs/ar \
            --github-token ${{ secrets.GITHUB_TOKEN }} \
            './**/*.md' \
            './**/*.html' \
            './**/*.yml' \
            './**/*.yaml' \
            './**/*.py' \
            './**/*.ipynb'
